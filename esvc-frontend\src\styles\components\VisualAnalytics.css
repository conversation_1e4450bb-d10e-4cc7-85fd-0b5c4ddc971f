/* Visual Analytics Container */
.visual-analytics-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.visual-analytics-content {
  padding: 0;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 40px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.page-title {
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.title-with-span {
  position: relative;
  display: inline-block;
  margin-right: 16px;
}

.title-span {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Dashboard Layout - Reuse from Overview */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar - Reuse from Overview */
.dashboard-sidebar {
  width: 280px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Montserrat', sans-serif;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  line-height: 1.5;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidebar-item.active {
  background: #BF4129;
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.sidebar-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.sidebar-item:hover .sidebar-icon,
.sidebar-item.active .sidebar-icon {
  filter: brightness(1);
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.visual-analytics-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Charts Container */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Chart Card */
.chart-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.timeframe-select {
  background: rgba(64, 64, 64, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
}

.timeframe-select:hover {
  background: rgba(64, 64, 64, 1);
  border-color: rgba(255, 255, 255, 0.2);
}

.timeframe-select option {
  background: #404040;
  color: #FFFFFF;
}

/* Chart Wrapper */
.chart-wrapper {
  width: 100%;
  height: 300px;
}

/* Pie Chart Specific Styles */
.pie-chart-wrapper {
  display: flex;
  gap: 32px;
  align-items: center;
}

.pie-chart-container {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.pie-chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-holdings {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.total-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.total-value {
  font-size: 18px;
  color: #FFFFFF;
  font-weight: 700;
}

.pie-chart-legend {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 200px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.legend-name {
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 500;
}

.legend-amount {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 400;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 0 20px;
    margin-bottom: 32px;
  }

  .page-title {
    font-size: 32px;
  }

  .title-span {
    width: 80px;
    bottom: -6px;
  }

  .page-subtitle {
    font-size: 16px;
  }

  .dashboard-layout {
    flex-direction: column;
    gap: 24px;
    padding: 0 20px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    padding: 8px 12px;
    height: 60px;
    margin-bottom: 20px;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 6px;
    padding-bottom: 4px;
    scroll-behavior: smooth;
    align-items: center;
    justify-content: flex-start;
  }

  .sidebar-item {
    flex-shrink: 0;
    min-width: 110px;
    max-width: 120px;
    justify-content: center;
    padding: 8px 12px;
    font-size: 12px;
    height: 44px;
    border-radius: 6px;
  }

  .sidebar-label {
    font-size: 11px;
  }

  .sidebar-icon {
    width: 18px;
    height: 18px;
  }

  .dashboard-content {
    max-width: 100%;
    order: 2;
  }

  .section-title {
    font-size: 24px;
  }

  .chart-card {
    padding: 16px;
  }

  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .chart-title {
    font-size: 18px;
  }

  .pie-chart-wrapper {
    flex-direction: column;
    gap: 0;
    align-items: center;
    background: rgba(38, 38, 38, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 20px;
    margin: 0 auto;
    max-width: 320px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .pie-chart-container {
    min-width: auto;
    width: 100%;
    max-width: 280px;
    height: 280px;
    margin-bottom: 16px;
  }

  .pie-chart-container .recharts-wrapper {
    height: 280px !important;
  }

  .pie-chart-center {
    top: 50%;
    left: 50%;
  }

  .total-label {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .total-value {
    font-size: 16px;
  }

  .pie-chart-legend {
    min-width: auto;
    width: 100%;
    max-width: 280px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
  }

  .legend-item:last-child {
    border-bottom: none;
  }

  .legend-info {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .legend-name {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    color: #FFFFFF;
  }

  .legend-details {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: auto;
  }

  .legend-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #FFFFFF;
  }

  .legend-amount {
    font-size: 10px;
    opacity: 0.7;
    color: rgba(255, 255, 255, 0.7);
  }
}

/* Extra small mobile screens */
@media (max-width: 480px) {
  .pie-chart-wrapper {
    max-width: 300px;
    padding: 16px;
  }

  .pie-chart-container {
    max-width: 260px;
    height: 260px;
  }

  .pie-chart-container .recharts-wrapper {
    height: 260px !important;
  }

  .total-label {
    font-size: 9px;
  }

  .total-value {
    font-size: 14px;
  }

  .pie-chart-legend {
    max-width: 260px;
    gap: 6px;
  }

  .legend-item {
    padding: 0;
  }

  .legend-name {
    font-size: 10px;
  }

  .legend-percentage {
    font-size: 10px;
  }

  .legend-amount {
    font-size: 8px;
  }
}
