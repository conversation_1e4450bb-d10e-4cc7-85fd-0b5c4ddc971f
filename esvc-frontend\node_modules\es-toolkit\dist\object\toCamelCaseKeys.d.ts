type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}` ? `${Lowercase<P1>}${Uppercase<P2>}${CamelCase<P3>}` : Lowercase<S>;
type ToCamelCaseKeys<T> = T extends any[] ? Array<ToCamelCaseKeys<T[number]>> : T extends Record<string, any> ? {
    [K in keyof T as CamelCase<string & K>]: ToCamelCaseKeys<T[K]>;
} : T;
/**
 * Creates a new object composed of the properties with keys converted to camelCase.
 *
 * This function takes an object and returns a new object that includes the same properties,
 * but with all keys converted to camelCase format.
 *
 * @template T - The type of object.
 * @param {T} obj - The object to convert keys from.
 * @returns {ToCamelCaseKeys<T>} A new object with all keys converted to camelCase.
 *
 * @example
 * // Example with objects
 * const obj = { user_id: 1, first_name: '<PERSON>' };
 * const result = toCamelCaseKeys(obj);
 * // result will be { userId: 1, firstName: '<PERSON>' }
 *
 * // Example with arrays of objects
 * const arr = [
 *   { user_id: 1, first_name: '<PERSON>' },
 *   { user_id: 2, first_name: 'Jane' }
 * ];
 * const arrResult = toCamelCaseKeys(arr);
 * // arrResult will be [{ userId: 1, firstName: 'John' }, { userId: 2, firstName: 'Jane' }]
 *
 * // Example with nested objects
 * const nested = {
 *   user_data: {
 *     user_id: 1,
 *     user_address: {
 *       street_name: 'Main St',
 *       zip_code: '12345'
 *     }
 *   }
 * };
 * const nestedResult = toCamelCaseKeys(nested);
 * // nestedResult will be:
 * // {
 * //   userData: {
 * //     userId: 1,
 * //     userAddress: {
 * //       streetName: 'Main St',
 * //       zipCode: '12345'
 * //     }
 * //   }
 * // }
 */
declare function toCamelCaseKeys<T>(obj: T): ToCamelCaseKeys<T>;

export { toCamelCaseKeys };
