/* ROI Distribution Content */
.roi-distribution-content {
  position: relative;
  z-index: 2;
}

/* Page Header - Reuse from Overview */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout - Reuse from Overview */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Sidebar - Reuse from Overview */
.dashboard-sidebar {
  width: 240px;
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  height: fit-content;
  position: sticky;
  top: 140px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-radius: 8px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
  position: relative;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}

.sidebar-item.active {
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  color: #FFFFFF;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.sidebar-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.sidebar-item:hover .sidebar-icon,
.sidebar-item.active .sidebar-icon {
  filter: brightness(1);
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.roi-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Stats Cards */
.roi-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 40px;
}

.stats-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 24px;
  flex: 1;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.stats-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.stats-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 12px;
}

/* Stats Change */
.stats-change {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #4AFF4A;
}

.trend-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

/* Chart Section */
.chart-section {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.chart-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.chart-filter {
  position: relative;
}

.filter-select {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 40px 12px 16px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  min-width: 140px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(191, 65, 41, 0.5);
}

/* Chart Container */
.chart-container {
  height: 400px;
  width: 100%;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
    gap: 12px;
    justify-content: center;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 180px;
  }

  .page-subtitle {
    font-size: 14px;
    line-height: 22px;
    padding: 0 10px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-sidebar {
    width: 100%;
    position: static;
    order: 1;
    top: auto;
    padding: 12px 16px;
    margin-bottom: 24px;
    border-radius: 12px;
    height: 80px;
    display: flex;
    align-items: center;
  }

  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 16px;
    padding: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
  }

  .sidebar-nav::-webkit-scrollbar {
    display: none;
  }

  .sidebar-item {
    white-space: nowrap;
    min-width: 120px;
    max-width: 160px;
    padding: 0 14px;
    flex-shrink: 0;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.2;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-sizing: border-box;
  }

  .sidebar-icon {
    width: 16px;
    height: 16px;
  }

  .dashboard-content {
    order: 2;
  }

  .roi-stats {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
  }

  .stats-card {
    padding: 20px;
    border-radius: 16px;
  }

  .stats-label {
    font-size: 11px;
    margin-bottom: 10px;
  }

  .stats-value {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .stats-change {
    font-size: 13px;
  }

  .chart-section {
    padding: 20px;
    border-radius: 16px;
    background: rgba(38, 38, 38, 0.6);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 24px;
  }

  .chart-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .chart-filter {
    width: 100%;
  }

  .filter-select {
    width: 100%;
    min-width: auto;
    padding: 12px 40px 12px 16px;
    font-size: 14px;
  }

  .chart-container {
    height: 280px;
    margin: 0 -10px;
    padding: 0 10px;
  }

  /* Ensure chart text is readable on mobile */
  .chart-container .recharts-text {
    font-size: 11px !important;
    font-family: 'Montserrat', sans-serif !important;
  }

  .chart-container .recharts-cartesian-axis-tick-value {
    font-size: 10px !important;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .page-title {
    font-size: 36px;
  }

  .dashboard-layout {
    padding: 0 16px;
  }

  .chart-section {
    padding: 16px;
  }

  .chart-container {
    height: 260px;
    margin: 0 -8px;
    padding: 0 8px;
  }

  .stats-value {
    font-size: 24px;
  }

  .chart-title {
    font-size: 16px;
  }
}
