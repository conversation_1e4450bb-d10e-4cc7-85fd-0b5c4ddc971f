import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from './DashboardLayout';
import '../styles/components/LiveReserve.css';

// Import icons
import overviewIcon from '../assets/overview.png';
import walletMoneyIcon from '../assets/wallet-money.png';
import arrow2Icon from '../assets/arrow-2.png';
import cardCoinIcon from '../assets/card-coin.png';
import moneysIcon from '../assets/moneys.png';
import percentageSquareIcon from '../assets/percentage-square.png';
import chartIcon from '../assets/chart.png';
import spanImage from '../assets/span.png';
import trendUpIcon from '../assets/trend-up.png';
import trendDownIcon from '../assets/trend-down.png';

// Import crypto asset icons
import bitcoinSymbol from '../assets/bitcoin_symbol.png.png';
import solanaIcon from '../assets/solana_icon.jpeg.png';
import usdcIcon from '../assets/usdc.png';
import esvcToken from '../assets/esvc-token.png';

interface LiveReserveProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const LiveReserve: React.FC<LiveReserveProps> = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('live-reserve');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Center active item on mobile
  useEffect(() => {
    if (isMobile) {
      const activeElement = document.querySelector('.sidebar-item.active') as HTMLElement;
      const sidebarNav = document.querySelector('.sidebar-nav') as HTMLElement;

      if (activeElement && sidebarNav) {
        const activeRect = activeElement.getBoundingClientRect();
        const navRect = sidebarNav.getBoundingClientRect();
        const scrollLeft = activeElement.offsetLeft - (navRect.width / 2) + (activeRect.width / 2);

        sidebarNav.scrollTo({
          left: scrollLeft,
          behavior: 'smooth'
        });
      }
    }
  }, [activeTab, isMobile]);

  const sidebarItems = [
    { id: 'overview', label: 'Overview', icon: overviewIcon },
    { id: 'live-reserve', label: 'Live Reserve', icon: walletMoneyIcon, active: true },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: arrow2Icon },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: cardCoinIcon },
    { id: 'startup-funding', label: 'Startup Funding', icon: moneysIcon },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: percentageSquareIcon },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: chartIcon }
  ];

  const holdingsCards = [
    {
      title: 'BTC HOLDINGS',
      value: '$91,000',
      unit: 'BTC',
      change: '****% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      iconImage: bitcoinSymbol,
      iconClass: 'btc'
    },
    {
      title: 'SOLANA HOLDINGS',
      value: '$124,000',
      unit: 'SOL',
      change: '****% Today',
      changeType: 'positive',
      changeIcon: trendUpIcon,
      iconImage: solanaIcon,
      iconClass: 'sol'
    },
    {
      title: 'USDC HOLDINGS',
      value: '$51,500',
      unit: 'USDC',
      change: '-0.9% Today',
      changeType: 'negative',
      changeIcon: trendDownIcon,
      iconImage: usdcIcon,
      iconClass: 'usdc'
    },
    {
      title: 'ESVC RESERVES',
      value: '$51,500',
      unit: 'ESVC',
      change: '-0.9% Today',
      changeType: 'negative',
      changeIcon: trendDownIcon,
      iconImage: esvcToken,
      iconClass: 'esvc'
    }
  ];

  const totalValue = {
    title: 'TOTAL VALUE OF ALL HOLDINGS',
    value: '$318,000.00',
    change: '****% Today',
    changeType: 'positive',
    changeIcon: trendUpIcon
  };

  const handleSidebarClick = (itemId: string) => {
    setActiveTab(itemId);
    
    // Navigate to different pages based on the tab
    switch (itemId) {
      case 'overview':
        navigate('/overview');
        break;
      case 'live-reserve':
        // Already on this page
        break;
      case 'daily-transactions':
        navigate('/daily-transactions');
        break;
      case 'real-time-staking':
        navigate('/real-time-staking');
        break;
      case 'startup-funding':
        navigate('/startup-funding');
        break;
      case 'roi-distribution':
        navigate('/roi-distribution');
        break;
      case 'visual-analytics':
        navigate('/visual-analytics');
        break;
      default:
        console.log(`Loading ${itemId} page...`);
    }
  };

  return (
    <DashboardLayout className="live-reserve-container">
      <div className="live-reserve-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        {/* Dashboard Layout */}
        <div className="dashboard-layout">
          {/* Sidebar */}
          <div className="dashboard-sidebar">
            <nav className="sidebar-nav">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  className={`sidebar-item ${activeTab === item.id ? 'active' : ''}`}
                  onClick={() => handleSidebarClick(item.id)}
                >
                  <img src={item.icon} alt={item.label} className="sidebar-icon" />
                  {item.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Dashboard Content */}
          <div className="dashboard-content">
            <div className="live-reserve-header">
              <h2 className="section-title">Live Reserve</h2>
            </div>
            
            <div className="holdings-grid">
              {holdingsCards.map((card, index) => (
                <div key={index} className="holding-card">
                  <div className="holding-icon-container">
                    <img src={card.iconImage} alt={card.title} className={`holding-icon ${card.iconClass}`} />
                  </div>
                  <div className="holding-info">
                    <h3 className="holding-title">{card.title}</h3>
                    <div className="holding-value">
                      {card.value}
                      <span className="holding-unit">{card.unit}</span>
                    </div>
                    <div className={`holding-change ${card.changeType}`}>
                      <img src={card.changeIcon} alt="Change indicator" className="change-icon" />
                      {card.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Total Value Card */}
            <div className="total-value-card">
              <h3 className="total-title">{totalValue.title}</h3>
              <div className="total-value">{totalValue.value}</div>
              <div className={`total-change ${totalValue.changeType}`}>
                <img src={totalValue.changeIcon} alt="Change indicator" className="change-icon" />
                {totalValue.change}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default LiveReserve;
